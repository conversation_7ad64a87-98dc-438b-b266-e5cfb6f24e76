<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-8">
        <div class="card">
          <div class="card-header">
            <h5 class="title">Modifier le Profil</h5>
          </div>
          <div class="card-body">
            <form [formGroup]="profilForm" (ngSubmit)="onSubmit()">
              <div class="row">
                <div class="col-md-6 pr-1">
                  <div class="form-group">
                    <label>Nom</label>
                    <input type="text" 
                           class="form-control" 
                           placeholder="Nom"
                           formControlName="nom"
                           [class.is-invalid]="f['nom'].invalid && f['nom'].touched">
                    <div class="invalid-feedback" *ngIf="f['nom'].invalid && f['nom'].touched">
                      Le nom est requis (max 100 caractères)
                    </div>
                  </div>
                </div>
                <div class="col-md-6 pl-1">
                  <div class="form-group">
                    <label>Prénom</label>
                    <input type="text" 
                           class="form-control" 
                           placeholder="Prénom"
                           formControlName="prenom"
                           [class.is-invalid]="f['prenom'].invalid && f['prenom'].touched">
                    <div class="invalid-feedback" *ngIf="f['prenom'].invalid && f['prenom'].touched">
                      Le prénom est requis (max 100 caractères)
                    </div>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-12">
                  <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" 
                           class="form-control" 
                           placeholder="Email"
                           formControlName="email"
                           [class.is-invalid]="f['email'].invalid && f['email'].touched">
                    <div class="invalid-feedback" *ngIf="f['email'].invalid && f['email'].touched">
                      Email requis et valide
                    </div>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-12">
                  <div class="form-group">
                    <label>Téléphone</label>
                    <input type="text" 
                           class="form-control" 
                           placeholder="Téléphone"
                           formControlName="telephone"
                           [class.is-invalid]="f['telephone'].invalid && f['telephone'].touched">
                    <div class="invalid-feedback" *ngIf="f['telephone'].invalid && f['telephone'].touched">
                      Maximum 20 caractères
                    </div>
                  </div>
                </div>
              </div>
              <button type="submit" 
                      class="btn btn-primary btn-round"
                      [disabled]="loading || profilForm.invalid">
                <span *ngIf="loading" class="spinner-border spinner-border-sm mr-2"></span>
                Mettre à jour le profil
              </button>
            </form>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="card card-user">
          <div class="image">
            <img src="./assets/img/bg/fabio-mangione.jpg" alt="...">
          </div>
          <div class="card-body">
            <div class="author">
              <a href="#">
                <img class="avatar border-gray" src="./assets/img/faces/face-3.jpg" alt="...">
                <h5 class="title" *ngIf="currentUser">{{currentUser.prenom}} {{currentUser.nom}}</h5>
              </a>
              <p class="description" *ngIf="currentUser">
                <span class="badge" [ngClass]="getRoleBadgeClass()">
                  {{currentUser.role}}
                </span>
              </p>
            </div>
            <p class="description text-center" *ngIf="currentUser">
              {{currentUser.email}}
              <br *ngIf="currentUser.telephone">
              <span *ngIf="currentUser.telephone">{{currentUser.telephone}}</span>
            </p>
          </div>
          <hr>
          <div class="button-container">
            <button class="btn btn-neutral btn-icon btn-round btn-lg">
              <i class="fa fa-facebook-square"></i>
            </button>
            <button class="btn btn-neutral btn-icon btn-round btn-lg">
              <i class="fa fa-twitter"></i>
            </button>
            <button class="btn btn-neutral btn-icon btn-round btn-lg">
              <i class="fa fa-google-plus-square"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

