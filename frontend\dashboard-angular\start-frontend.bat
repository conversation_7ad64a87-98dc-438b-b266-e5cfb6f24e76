@echo off
echo 🚀 Lancement du Frontend SprintBot...
echo.

REM Vérifier si Node.js est installé
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js n'est pas installé ou pas dans le PATH
    echo Veuillez installer Node.js depuis https://nodejs.org/
    pause
    exit /b 1
)

REM Vérifier si Angular CLI est installé
ng version >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 Installation d'Angular CLI...
    npm install -g @angular/cli
)

REM Installer les dépendances si nécessaire
if not exist "node_modules" (
    echo 📦 Installation des dépendances...
    npm install --legacy-peer-deps
)

echo ✅ Démarrage du serveur de développement...
echo 🌐 L'application sera disponible sur http://localhost:4200
echo.
echo Appuyez sur Ctrl+C pour arrêter le serveur
echo.

REM Lancer le serveur de développement
ng serve --open

pause
