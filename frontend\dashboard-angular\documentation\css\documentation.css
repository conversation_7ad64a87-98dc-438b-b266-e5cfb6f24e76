.tim-row{
    margin-bottom: 40px;
}
.title-container img {
    width: 80px;
    height: 80px;
}
.navbar-center {
    float: none;
    display: inline-block;
}
.navbar-title .image-container {
    height: 40px;
    width: 40px;
    display: inline-block;
    top: -3px;
    position: relative;
}
.navbar-title img {
    width: 100%;
}
.navbar-title h4 {
    line-height: 12px;
    margin-top: 17px;
    margin-bottom: 5px;
}
.navbar-transparent .collapse .navbar-right > li a{
    color: #fff!important;
}
pre.prettyprint{
    background-color: #eee;
    border: 0px;
    margin-bottom: 0;
    margin-top: 20px;
    padding: 20px;
    text-align: left;
}
.atv, .str{
    color: #05AE0E;
}
.tag, .pln, .kwd{
    color: #3472F7;
}
.atn{
    color: #2C93FF;
}
.pln{
    color: #333;
}
.com{
    color: #999;
}
.space-top{
    margin-top: 50px;
}
.btn-primary .caret{
    border-top-color: #3472F7;
    color: #3472F7;
}
.area-line{
    border: 1px solid #999;
    border-left: 0;
    border-right: 0;
    color: #666;
    display: block;
    margin-top: 20px;
    padding: 8px 0;
    text-align: center;
}
.area-line a{
    color: #666;
}
.container-fluid{
    padding-right: 15px;
    padding-left: 15px;
}
.logo-container .logo{
    overflow: hidden;
    border-radius: 50%;
    border: 1px solid #333333;
    width: 50px;
    float: left;
}
.header-wrapper {
  position: relative;
  height: 450px;
}

.header-wrapper .navbar {
    border-radius: 0;
    position: absolute;
    width: 100%;
    z-index: 3;
}
.header-wrapper .header {
    background-color: #ff8f5e;
    background-position: center center;
    background-size: cover;
    height: 450px;
    overflow: hidden;
    position: absolute;
    width: 100%;
    z-index: 1;
}
.header-wrapper .header .filter::after {
/*     background: rgba(0, 0, 0, 0) linear-gradient(to bottom, #9368e9 0%, #943bea 100%) repeat scroll 0 0 / 150% 150%;     */
    content: "";
    display: block;
    height: 450px;
    left: 0;
    opacity: 0.77;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 2;
}
.header-wrapper .title-container{
    color: #fff;
    position: relative;
    top: 150px;
    z-index: 3;

}
.logo-container .brand{
    font-size: 18px;
    color: #FFFFFF;
    line-height: 20px;
    float: left;
    margin-left: 10px;
    margin-top: 5px;
    width: 50px;
    height: 50px;
}
.navbar.navbar-transparent .logo-container .brand{
    color: #FFFFFF!important;
}
.logo-container{
    margin-top: 10px;
    margin-left: 15px;
}
.logo-container .logo img{
    width: 100%;
}
.navbar-small .logo-container .brand{
    color: #333333;
}
.fixed-section{
    top: 57px;
    max-height: 80vh;
    overflow: scroll;
    padding-top: 90px;
}
.fixed-section ul li{
    list-style: none;
}
.fixed-section li a{
    font-size: 14px;
    padding: 2px;
    display: block;
    color: #666666;
}
.fixed-section li a.active{
    color: #00bbff;
}
.fixed-section.float{
    position: fixed;
    top: 100px;
    width: 200px;
    margin-top: 0;
}

.table-bigboy .img-container{
    width: 130px;
    height: 85px;
}

.table-bigboy .td-name{
    min-width: 170px;
}
#buttons-row .btn{
    margin-bottom: 15px;
}
@media (max-width: 991px){
    .fixed-section.affix {
        position: relative;
        margin-bottom: 100px;
    }
    .navbar.navbar-transparent button span{
        background-color: #fff!important;
    }
    .navbar.navbar-transparent .logo-container .brand{
        color: #FFFFFF!important;
    }
}
@media (max-width: 768px) {=

  /*.navbar.navbar-transparent {
    background-color: rgba(0, 0, 0, 0);
    padding-top: 10px;
    border-radius: 0;
  }*/
}
