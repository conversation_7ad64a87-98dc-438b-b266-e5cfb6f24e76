import { Component, OnInit } from '@angular/core';

declare interface TableData {
    headerRow: string[];
    dataRows: string[][];
}

@Component({
    selector: 'table-cmp',
    moduleId: module.id,
    templateUrl: 'table.component.html'
})

export class TableComponent implements OnInit{
    public tableData1: TableData;
    public tableData2: TableData;
    ngOnInit(){
        this.tableData1 = {
            headerRow: [ 'ID', 'Name', 'Country', 'City', 'Salary'],
            dataRows: [
                ['1', '<PERSON> Rice', 'Niger', 'Oud-Turnhout', '$36,738'],
                ['2', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>aai-Waas', '$23,789'],
                ['3', '<PERSON>', 'Netherlands', '<PERSON><PERSON>ux', '$56,142'],
                ['4', '<PERSON>', 'Korea, South', 'Overland Park', '$38,735'],
                ['5', '<PERSON>', 'Malawi', 'Feld<PERSON><PERSON> in Kärnten', '$63,542'],
                ['6', '<PERSON>', 'Chile', 'Gloucester', '$78,615']
            ]
        };
        this.tableData2 = {
            headerRow: [ 'ID', 'Name',  '<PERSON><PERSON>', 'Country', 'City' ],
            dataRows: [
                ['1', '<PERSON> <PERSON>','$36,738', 'Niger', 'Oud-<PERSON><PERSON>' ],
                ['2', '<PERSON><PERSON> <PERSON>', '$23,789', '<PERSON><PERSON><PERSON>o', '<PERSON>aai-Waas'],
                ['3', '<PERSON>', '$56,142', 'Netherlands', '<PERSON>le<PERSON>' ],
                ['4', '<PERSON> <PERSON>ey', '$38,735', 'Korea, South', 'Overland Park' ],
                ['5', 'Doris Greene', '$63,542', 'Malawi', 'Feldkirchen in Kärnten', ],
                ['6', 'Mason Porter', '$78,615', 'Chile', 'Gloucester' ]
            ]
        };
    }
}
