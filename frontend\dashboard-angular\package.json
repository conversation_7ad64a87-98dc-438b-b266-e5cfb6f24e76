{"name": "paper-dashboard-angular", "version": "2.4.0", "license": "MIT", "scripts": {"ng": "ng", "start": "ng serve -o", "build": "cross-env CI=false ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "install:clean": "rm -rf node_modules/ && rm -rf package-lock.json && npm install && npm start"}, "private": true, "dependencies": {"@angular/animations": "^14.2.0", "@angular/cdk": "^14.2.0", "@angular/common": "^14.2.0", "@angular/compiler": "^14.2.0", "@angular/core": "^14.2.0", "@angular/elements": "^14.2.0", "@angular/forms": "^14.2.0", "@angular/google-maps": "^14.2.0", "@angular/localize": "^14.2.0", "@angular/material": "^14.2.0", "@angular/platform-browser": "^14.2.0", "@angular/platform-browser-dynamic": "^14.2.0", "@angular/router": "^14.2.0", "@ng-bootstrap/ng-bootstrap": "12.0.1", "@popperjs/core": "^2.11.4", "@ngui/map": "0.30.3", "@types/googlemaps": "3.39.14", "arrive": "2.4.1", "bootstrap": "4.5.2", "bootstrap-notify": "3.1.3", "chart.js": "2.9.4", "chartist": "0.11.4", "express": "4.18.0", "jquery": "3.5.1", "moment": "2.29.1", "ngx-toastr": "14.3.0", "rxjs": "~7.5.0", "zone.js": "~0.11.4", "web-animations-js": "2.3.2"}, "devDependencies": {"@angular-devkit/build-angular": "^14.2.7", "@angular/cli": "~14.2.7", "@angular/compiler-cli": "^14.2.0", "@angular/language-service": "14.2.0", "@types/jasmine": "~4.0.0", "@types/jasminewd2": "~2.0.10", "@types/node": "^17.0.21", "codelyzer": "6.0.2", "jasmine-core": "~4.4.0", "jasmine-spec-reporter": "~7.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-coverage-istanbul-reporter": "~3.0.3", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "protractor": "7.0.0", "ts-node": "~10.9.1", "typescript": "~4.7.2", "cross-env": "^7.0.3"}}