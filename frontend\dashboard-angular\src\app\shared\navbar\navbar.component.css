/* Navbar Component Styles */

.navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1rem 0;
}

.navbar-brand {
  color: white !important;
  font-weight: 700;
  font-size: 1.5rem;
  text-decoration: none;
}

.navbar-brand:hover {
  color: rgba(255, 255, 255, 0.9) !important;
}

.navbar-toggler {
  border: 2px solid white;
  border-radius: 8px;
  padding: 0.5rem;
  background: transparent;
}

.navbar-toggler:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

.navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.navbar-nav {
  align-items: center;
}

.nav-link {
  color: white !important;
  font-weight: 500;
  padding: 0.5rem 1rem !important;
  border-radius: 6px;
  transition: background-color 0.3s ease;
  margin: 0 0.25rem;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white !important;
}

.nav-link.active {
  background-color: rgba(255, 255, 255, 0.2);
  color: white !important;
}

.dropdown-menu {
  border: none;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  margin-top: 0.5rem;
}

.dropdown-item {
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  color: #667eea;
}

.dropdown-divider {
  margin: 0.5rem 0;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid white;
  margin-right: 0.5rem;
}

.notification-badge {
  background-color: #dc3545;
  color: white;
  border-radius: 50%;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  position: absolute;
  top: -5px;
  right: -5px;
}

.search-form {
  position: relative;
  margin: 0 1rem;
}

.search-input {
  background-color: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  color: white;
  padding: 0.5rem 1rem;
  width: 250px;
  transition: all 0.3s ease;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.search-input:focus {
  background-color: white;
  color: #333;
  border-color: white;
  box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

.search-input:focus::placeholder {
  color: #999;
}

@media (max-width: 768px) {
  .search-form {
    margin: 1rem 0;
    width: 100%;
  }
  
  .search-input {
    width: 100%;
  }
}
