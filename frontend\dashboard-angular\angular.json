{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"pd-free-angularcli": {"root": "", "sourceRoot": "src", "projectType": "application", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "tsConfig": "src/tsconfig.app.json", "polyfills": "src/polyfills.ts", "allowedCommonJsDependencies": ["chart.js"], "assets": ["src/assets", "src/favicon.ico"], "styles": ["node_modules/bootstrap/scss/bootstrap.scss", "src/assets/scss/paper-dashboard.scss", "node_modules/ngx-toastr/toastr.css", "src/assets/css/demo.css"], "scripts": []}, "configurations": {"production": {"optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "development": {"vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "buildOptimizer": false, "optimization": {"scripts": true, "styles": {"minify": false, "inlineCritical": true}, "fonts": true}, "outputHashing": "all"}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "pd-free-angularcli:build"}, "configurations": {"production": {"browserTarget": "pd-free-angularcli:build:production"}, "development": {"browserTarget": "pd-free-angularcli:build:development"}, "defaultConfiguration": "development"}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "pd-free-angularcli:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "karmaConfig": "./karma.conf.js", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "scripts": ["node_modules/jquery/dist/jquery.js", "node_modules/bootstrap/dist/js/bootstrap.js", "node_modules/arrive/src/arrive.js", "node_modules/moment/moment.js", "node_modules/chartist/dist/chartist.js", "node_modules/bootstrap-notify/bootstrap-notify.js"], "styles": ["src/styles.css", "src/assets/sass/paper-dashboard.scss", "src/assets/css/demo.css"], "assets": ["src/assets", "src/favicon.ico"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"], "exclude": []}}}}, "pd-free-angularcli-e2e": {"root": "e2e", "sourceRoot": "e2e", "projectType": "application", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "./protractor.conf.js", "devServerTarget": "pd-free-angularcli:serve"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["e2e/tsconfig.e2e.json"], "exclude": []}}}}}, "defaultProject": "pd-free-angularcli", "schematics": {"@schematics/angular:component": {"prefix": "app", "styleext": "scss"}, "@schematics/angular:directive": {"prefix": "app"}}, "cli": {"analytics": false}}