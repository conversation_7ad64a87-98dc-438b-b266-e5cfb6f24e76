#!/bin/bash

echo "🚀 Lancement du Frontend SprintBot..."
echo

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
    echo "❌ Node.js n'est pas installé"
    echo "Veuillez installer Node.js depuis https://nodejs.org/"
    exit 1
fi

# Vérifier si Angular CLI est installé
if ! command -v ng &> /dev/null; then
    echo "📦 Installation d'Angular CLI..."
    npm install -g @angular/cli
fi

# Installer les dépendances si nécessaire
if [ ! -d "node_modules" ]; then
    echo "📦 Installation des dépendances..."
    npm install --legacy-peer-deps
fi

echo "✅ Démarrage du serveur de développement..."
echo "🌐 L'application sera disponible sur http://localhost:4200"
echo
echo "Appuyez sur Ctrl+C pour arrêter le serveur"
echo

# Lancer le serveur de développement
ng serve --open
